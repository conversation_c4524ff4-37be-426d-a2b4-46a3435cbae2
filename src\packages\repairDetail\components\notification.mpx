<template>
  <t-popup
    visible="{{visible}}"
    bind:visible-change="onVisibleChange"
    placement="bottom"
  >
    <view class="notification"> </view>
  </t-popup>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { TechMaintainUrl } from 'shared/assets/imageUrl';
  import TechMaintenanceApi from 'shared/api/techMaintenance';
  import { addGlobalEventListener } from 'shared/utils/emit';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';

  createComponent({
    properties: {
      visible: {
        type: Boolean,
        value: false
      }
    },
    data: {
      techMaintenanceApi: new TechMaintenanceApi(),
      TechMaintainUrl
    },
    lifetimes: {
      created() {
        addGlobalEventListener('getRepairOrderNumber', orderNumber => {
          this.orderNumber = orderNumber;
        });
      },
      attached() {},
      detached() {},
      ready() {}
    },
    computed: {},
    watch: {},
    methods: {
      onVisibleChange() {
        console.log('关闭弹窗');
        this.triggerEvent('close');
      }
    }
  });
</script>

<style lang="scss" scoped>
  .notification {
    height: 1000rpx;

    .block {
      width: 100vw;
      border: #fff 1px solid;
      border-top-left-radius: 16rpx;
      border-top-right-radius: 16rpx;
      background: linear-gradient(
        90deg,
        #e3e9fc 100%,
        #efecfb 100%,
        #f4e9f6 100%
      );
      .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 80rpx;
        padding: 0 24rpx;
      }
      .title {
        text-align: center;
        font-weight: 600;
        font-size: 32rpx;
      }
    }
  }
</style>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {
      "t-popup": "tdesign-miniprogram/popup/popup"
    }
  }
</script>
